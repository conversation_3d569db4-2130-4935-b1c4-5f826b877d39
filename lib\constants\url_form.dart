/// 定义URL格式常量，方便管理和维护
class UrlFormat {
  static const String apiBaseUrl = 'http://192.168.2.7:8000';
  static const String apiVersion = '/api';
  static const String apiAuth = '$apiBaseUrl$apiVersion/auth';
  static const String apiDevice = '$apiBaseUrl$apiVersion/device';
  static const String apiIot = '$apiBaseUrl$apiVersion/iot';
  static const String apiSensor = '$apiBaseUrl$apiVersion/sensor';
  static const String apiUser = '$apiBaseUrl$apiVersion/user';
  static const String apiAntiLost = '$apiBaseUrl$apiVersion/anti-lost';
  static const String apiAI = '$apiBaseUrl$apiVersion/ai';

  static const String AuthLogin = '$apiAuth/login/';
  static const String AuthLogout = '$apiAuth/logout/';
  static const String AuthRefreshToken = '$apiAuth/refresh-token/';
  static const String AuthSmsCode = '$apiAuth/smscode/';

  static const String DeviceInfo = '$apiDevice/info/';
  static const String DeviceBindUser = '$apiDevice/bind/';
  static const String DeviceUnbindUser = '$apiDevice/unbind/';
  static const String DeviceSyncInfo = '$apiDevice/sync/';
  static const String DeviceUploadAudio = '$apiDevice/upload_audio/';
  static const String DeviceRunningStatus = '$apiDevice/running_status/';

  static const String IotSendData = '$apiIot/data/';
  static const String IotSendCommand = '$apiIot/control/';

  static const String QrCodeBind = '$apiAntiLost/bind/';
  static const String QrCodeUnbind = '$apiAntiLost/unbind/';
  static const String QrCodeList = '$apiAntiLost/list/';

  static const String SensorlatestData = '$apiSensor/latest/';

  static const String Chatbot = '$apiAI/chat/';
}
