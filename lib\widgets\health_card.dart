import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'bubble_status_display.dart';

/// 健康卡片类型枚举
enum HealthCardType {
  small, // 小卡片（占半屏宽度）
  wide, // 宽卡片（占满屏宽度）
}

/// 通用健康卡片组件
class HealthCard extends StatelessWidget {
  final String title;
  final Widget content;
  final DateTime? timestamp;
  final HealthCardType cardType;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final double? elevation;

  const HealthCard({
    Key? key,
    required this.title,
    required this.content,
    this.timestamp,
    this.cardType = HealthCardType.small,
    this.onTap,
    this.backgroundColor,
    this.elevation,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: elevation ?? 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      color: backgroundColor,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(10),
        child: Padding(
          padding: EdgeInsets.all(12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题（左上角）
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black54,
                ),
              ),
              SizedBox(height: cardType == HealthCardType.small ? 8 : 16),

              // 主要内容（中间）
              Flexible(
                child: Center(
                  child: content,
                ),
              ),

              // 时间戳（底部浅灰色小字）
              if (timestamp != null) ...[
                SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      DateFormat('yyyy-MM-dd HH:mm:ss').format(timestamp!),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.black54,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// 数值显示健康卡片
class ValueHealthCard extends StatelessWidget {
  final String title;
  final String value;
  final String unit;
  final DateTime? timestamp;
  final HealthCardType cardType;
  final VoidCallback? onTap;
  final Widget? icon;
  final Color? valueColor;

  const ValueHealthCard({
    Key? key,
    required this.title,
    required this.value,
    required this.unit,
    this.timestamp,
    this.cardType = HealthCardType.small,
    this.onTap,
    this.icon,
    this.valueColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return HealthCard(
      title: title,
      cardType: cardType,
      timestamp: timestamp,
      onTap: onTap,
      content: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 数值和单位
          Row(
            children: [
              icon!,
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: valueColor,
                ),
              ),
              Text(
                unit,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.black54,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// 状态显示健康卡片
class StatusHealthCard extends StatelessWidget {
  final String title;
  final String status;
  final DateTime? timestamp;
  final HealthCardType cardType;
  final VoidCallback? onTap;
  final Color? statusColor;
  final bool useBubble;

  const StatusHealthCard({
    Key? key,
    required this.title,
    required this.status,
    this.timestamp,
    this.cardType = HealthCardType.wide,
    this.onTap,
    this.statusColor,
    this.useBubble = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return HealthCard(
      title: title,
      cardType: cardType,
      timestamp: timestamp,
      onTap: onTap,
      content: Center(
        child: useBubble
            ? BubbleStatusDisplay(
                text: status,
                backgroundColor: statusColor ?? Colors.blue.shade100,
                textColor:
                    statusColor != null ? Colors.white : Colors.blue.shade800,
                fontSize: 16,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              )
            : Text(
                status,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: statusColor ?? Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
      ),
    );
  }
}

/// 健康检测结果卡片
class HealthDetectionCard extends StatelessWidget {
  final String title;
  final List<String> detections;
  final DateTime? timestamp;
  final VoidCallback? onTap;
  final bool showDangerMark;

  const HealthDetectionCard({
    Key? key,
    required this.title,
    required this.detections,
    this.timestamp,
    this.onTap,
    this.showDangerMark = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return HealthCard(
      title: title,
      cardType: HealthCardType.wide,
      timestamp: timestamp,
      onTap: onTap,
      content: Center(
        child: HealthDetectionBubbles(
          detections: detections,
          showDangerMark: showDangerMark,
        ),
      ),
    );
  }
}

/// 卡路里健康卡片
class CalorieHealthCard extends ValueHealthCard {
  CalorieHealthCard({
    Key? key,
    required double calories,
    DateTime? timestamp,
    VoidCallback? onTap,
  }) : super(
          key: key,
          title: '卡路里消耗',
          value: calories.toStringAsFixed(1),
          unit: 'kcal',
          timestamp: timestamp,
          cardType: HealthCardType.small,
          onTap: onTap,
          icon:
              Icon(Icons.local_fire_department, size: 38, color: Colors.orange),
          valueColor: Colors.orange.shade700,
        );
}

/// 活动状态健康卡片
class ActivityStatusHealthCard extends StatusHealthCard {
  ActivityStatusHealthCard({
    Key? key,
    required String activityStatus,
    DateTime? timestamp,
    VoidCallback? onTap,
    Color? statusColor,
  }) : super(
          key: key,
          title: '当前活动状态',
          status: activityStatus,
          timestamp: timestamp,
          onTap: onTap,
          statusColor: statusColor,
        );
}

/// 多活动状态健康卡片
class MultipleActivityStatusHealthCard extends StatelessWidget {
  final List<String> activityStatuses;
  final List<Color> statusColors;
  final DateTime? timestamp;
  final VoidCallback? onTap;

  const MultipleActivityStatusHealthCard({
    Key? key,
    required this.activityStatuses,
    required this.statusColors,
    this.timestamp,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return HealthCard(
      title: '当前活动状态',
      cardType: HealthCardType.wide,
      timestamp: timestamp,
      onTap: onTap,
      content: Center(
        child: MultipleBubbleDisplay(
          bubbles: List.generate(
            activityStatuses.length,
            (index) => BubbleStatusDisplay.activity(
              text: activityStatuses[index],
              customColor: index < statusColors.length
                  ? statusColors[index]
                  : Colors.blue.shade600,
            ),
          ),
          wrap: true,
          spacing: 8,
          mainAxisAlignment: MainAxisAlignment.center,
        ),
      ),
    );
  }
}

/// 情绪状态健康卡片
class EmotionStatusHealthCard extends StatelessWidget {
  final String emotionStatus;
  final DateTime? timestamp;
  final VoidCallback? onTap;
  final Color? statusColor;

  const EmotionStatusHealthCard({
    Key? key,
    required this.emotionStatus,
    this.timestamp,
    this.onTap,
    this.statusColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return HealthCard(
      title: '当前情绪状态',
      cardType: HealthCardType.wide,
      timestamp: timestamp,
      onTap: onTap,
      content: Center(
        child: BubbleStatusDisplay.emotion(
          text: emotionStatus,
          customColor: statusColor,
        ),
      ),
    );
  }
}
